diff --git a/node_modules/@milkdown/preset-gfm/lib/index.es.js b/node_modules/@milkdown/preset-gfm/lib/index.es.js
index b8d871c..ad3815b 100644
--- a/node_modules/@milkdown/preset-gfm/lib/index.es.js
+++ b/node_modules/@milkdown/preset-gfm/lib/index.es.js
@@ -84,7 +84,7 @@ i(j.shortcuts, {
 });
 const S = Le({
   tableGroup: "block",
-  cellContent: "paragraph",
+  cellContent: "paragraph+",
   cellAttributes: {
     alignment: {
       default: "left",
@@ -200,7 +200,20 @@ const x = C("table_cell", () => ({
     match: (e) => e.type === "tableCell" && !e.is<PERSON><PERSON><PERSON>,
     runner: (e, t, n) => {
       const o = t.align;
-      e.openNode(n, { alignment: o }).openNode(e.schema.nodes.paragraph).next(t.children).closeNode().closeNode();
+      e.openNode(n, { alignment: o });
+      if (t.children && t.children.length > 0) {
+        const hasBlockContent = t.children.some((child) =>
+          child.type === "paragraph" || child.type === "heading" || child.type === "list"
+        );
+        if (hasBlockContent) {
+          e.next(t.children);
+        } else {
+          e.openNode(e.schema.nodes.paragraph).next(t.children).closeNode();
+        }
+      } else {
+        e.openNode(e.schema.nodes.paragraph).closeNode();
+      }
+      e.closeNode();
     }
   },
   toMarkdown: {
@@ -225,7 +238,20 @@ const I = C("table_header", () => ({
     match: (e) => e.type === "tableCell" && !!e.isHeader,
     runner: (e, t, n) => {
       const o = t.align;
-      e.openNode(n, { alignment: o }), e.openNode(e.schema.nodes.paragraph), e.next(t.children), e.closeNode(), e.closeNode();
+      e.openNode(n, { alignment: o });
+      if (t.children && t.children.length > 0) {
+        const hasBlockContent = t.children.some((child) =>
+          child.type === "paragraph" || child.type === "heading" || child.type === "list"
+        );
+        if (hasBlockContent) {
+          e.next(t.children);
+        } else {
+          e.openNode(e.schema.nodes.paragraph).next(t.children).closeNode();
+        }
+      } else {
+        e.openNode(e.schema.nodes.paragraph).closeNode();
+      }
+      e.closeNode();
     }
   },
   toMarkdown: {
diff --git a/node_modules/@milkdown/preset-gfm/src/node/table/schema.ts b/node_modules/@milkdown/preset-gfm/src/node/table/schema.ts
index 576377d..9fb02be 100644
--- a/node_modules/@milkdown/preset-gfm/src/node/table/schema.ts
+++ b/node_modules/@milkdown/preset-gfm/src/node/table/schema.ts
@@ -6,7 +6,7 @@ import { withMeta } from '../../__internal__'
 
 const originalSchema = tableNodes({
   tableGroup: 'block',
-  cellContent: 'paragraph',
+  cellContent: 'paragraph+',
   cellAttributes: {
     alignment: {
       default: 'left',
@@ -153,12 +153,33 @@ export const tableCellSchema = $nodeSchema('table_cell', () => ({
     match: (node) => node.type === 'tableCell' && !node.isHeader,
     runner: (state, node, type) => {
       const align = node.align as string
-      state
-        .openNode(type, { alignment: align })
-        .openNode(state.schema.nodes.paragraph as NodeType)
-        .next(node.children)
-        .closeNode()
-        .closeNode()
+      state.openNode(type, { alignment: align })
+      
+      // Handle cell content - if there are children, process them
+      if (node.children && node.children.length > 0) {
+        // Check if we need to wrap content in paragraphs
+        const hasBlockContent = node.children.some((child: any) => 
+          child.type === 'paragraph' || child.type === 'heading' || child.type === 'list'
+        )
+        
+        if (hasBlockContent) {
+          // Content already has block elements, process directly
+          state.next(node.children)
+        } else {
+          // Wrap inline content in a paragraph
+          state
+            .openNode(state.schema.nodes.paragraph as NodeType)
+            .next(node.children)
+            .closeNode()
+        }
+      } else {
+        // Empty cell, create empty paragraph
+        state
+          .openNode(state.schema.nodes.paragraph as NodeType)
+          .closeNode()
+      }
+      
+      state.closeNode()
     },
   },
   toMarkdown: {
@@ -188,9 +209,31 @@ export const tableHeaderSchema = $nodeSchema('table_header', () => ({
     runner: (state, node, type) => {
       const align = node.align as string
       state.openNode(type, { alignment: align })
-      state.openNode(state.schema.nodes.paragraph as NodeType)
-      state.next(node.children)
-      state.closeNode()
+      
+      // Handle cell content - if there are children, process them
+      if (node.children && node.children.length > 0) {
+        // Check if we need to wrap content in paragraphs
+        const hasBlockContent = node.children.some((child: any) => 
+          child.type === 'paragraph' || child.type === 'heading' || child.type === 'list'
+        )
+        
+        if (hasBlockContent) {
+          // Content already has block elements, process directly
+          state.next(node.children)
+        } else {
+          // Wrap inline content in a paragraph
+          state
+            .openNode(state.schema.nodes.paragraph as NodeType)
+            .next(node.children)
+            .closeNode()
+        }
+      } else {
+        // Empty cell, create empty paragraph
+        state
+          .openNode(state.schema.nodes.paragraph as NodeType)
+          .closeNode()
+      }
+      
       state.closeNode()
     },
   },
diff --git a/node_modules/@milkdown/preset-gfm/src/node/table/schema.ts.backup b/node_modules/@milkdown/preset-gfm/src/node/table/schema.ts.backup
new file mode 100644
index 0000000..576377d
--- /dev/null
+++ b/node_modules/@milkdown/preset-gfm/src/node/table/schema.ts.backup
@@ -0,0 +1,215 @@
+import { tableNodes } from '@milkdown/prose/tables'
+import { $nodeSchema } from '@milkdown/utils'
+import type { MarkdownNode } from '@milkdown/transformer'
+import type { NodeType } from '@milkdown/prose/model'
+import { withMeta } from '../../__internal__'
+
+const originalSchema = tableNodes({
+  tableGroup: 'block',
+  cellContent: 'paragraph',
+  cellAttributes: {
+    alignment: {
+      default: 'left',
+      getFromDOM: (dom) => dom.style.textAlign || 'left',
+      setDOMAttr: (value, attrs) => {
+        attrs.style = `text-align: ${value || 'left'}`
+      },
+    },
+  },
+})
+
+/// Schema for table node.
+export const tableSchema = $nodeSchema('table', () => ({
+  ...originalSchema.table,
+  content: 'table_header_row table_row+',
+  disableDropCursor: true,
+  parseMarkdown: {
+    match: (node) => node.type === 'table',
+    runner: (state, node, type) => {
+      const align = node.align as (string | null)[]
+      const children = (node.children as MarkdownNode[]).map((x, i) => ({
+        ...x,
+        align,
+        isHeader: i === 0,
+      }))
+      state.openNode(type)
+      state.next(children)
+      state.closeNode()
+    },
+  },
+  toMarkdown: {
+    match: (node) => node.type.name === 'table',
+    runner: (state, node) => {
+      const firstLine = node.content.firstChild?.content
+      if (!firstLine) return
+
+      const align: (string | null)[] = []
+      firstLine.forEach((cell) => {
+        align.push(cell.attrs.alignment)
+      })
+      state.openNode('table', undefined, { align })
+      state.next(node.content)
+      state.closeNode()
+    },
+  },
+}))
+
+withMeta(tableSchema.node, {
+  displayName: 'NodeSchema<table>',
+  group: 'Table',
+})
+
+withMeta(tableSchema.ctx, {
+  displayName: 'NodeSchemaCtx<table>',
+  group: 'Table',
+})
+
+/// Schema for table header row node.
+export const tableHeaderRowSchema = $nodeSchema('table_header_row', () => ({
+  ...originalSchema.table_row,
+  disableDropCursor: true,
+  content: '(table_header)*',
+  parseDOM: [{ tag: 'tr[data-is-header]' }],
+  toDOM() {
+    return ['tr', { 'data-is-header': true }, 0]
+  },
+  parseMarkdown: {
+    match: (node) => Boolean(node.type === 'tableRow' && node.isHeader),
+    runner: (state, node, type) => {
+      const align = node.align as (string | null)[]
+      const children = (node.children as MarkdownNode[]).map((x, i) => ({
+        ...x,
+        align: align[i],
+        isHeader: node.isHeader,
+      }))
+      state.openNode(type)
+      state.next(children)
+      state.closeNode()
+    },
+  },
+  toMarkdown: {
+    match: (node) => node.type.name === 'table_header_row',
+    runner: (state, node) => {
+      state.openNode('tableRow', undefined, { isHeader: true })
+      state.next(node.content)
+      state.closeNode()
+    },
+  },
+}))
+
+withMeta(tableHeaderRowSchema.node, {
+  displayName: 'NodeSchema<tableHeaderRow>',
+  group: 'Table',
+})
+
+withMeta(tableHeaderRowSchema.ctx, {
+  displayName: 'NodeSchemaCtx<tableHeaderRow>',
+  group: 'Table',
+})
+
+/// Schema for table row node.
+export const tableRowSchema = $nodeSchema('table_row', () => ({
+  ...originalSchema.table_row,
+  disableDropCursor: true,
+  content: '(table_cell)*',
+  parseMarkdown: {
+    match: (node) => node.type === 'tableRow',
+    runner: (state, node, type) => {
+      const align = node.align as (string | null)[]
+      const children = (node.children as MarkdownNode[]).map((x, i) => ({
+        ...x,
+        align: align[i],
+      }))
+      state.openNode(type)
+      state.next(children)
+      state.closeNode()
+    },
+  },
+  toMarkdown: {
+    match: (node) => node.type.name === 'table_row',
+    runner: (state, node) => {
+      state.openNode('tableRow')
+      state.next(node.content)
+      state.closeNode()
+    },
+  },
+}))
+
+withMeta(tableRowSchema.node, {
+  displayName: 'NodeSchema<tableRow>',
+  group: 'Table',
+})
+
+withMeta(tableRowSchema.ctx, {
+  displayName: 'NodeSchemaCtx<tableRow>',
+  group: 'Table',
+})
+
+/// Schema for table cell node.
+export const tableCellSchema = $nodeSchema('table_cell', () => ({
+  ...originalSchema.table_cell,
+  disableDropCursor: true,
+  parseMarkdown: {
+    match: (node) => node.type === 'tableCell' && !node.isHeader,
+    runner: (state, node, type) => {
+      const align = node.align as string
+      state
+        .openNode(type, { alignment: align })
+        .openNode(state.schema.nodes.paragraph as NodeType)
+        .next(node.children)
+        .closeNode()
+        .closeNode()
+    },
+  },
+  toMarkdown: {
+    match: (node) => node.type.name === 'table_cell',
+    runner: (state, node) => {
+      state.openNode('tableCell').next(node.content).closeNode()
+    },
+  },
+}))
+
+withMeta(tableCellSchema.node, {
+  displayName: 'NodeSchema<tableCell>',
+  group: 'Table',
+})
+
+withMeta(tableCellSchema.ctx, {
+  displayName: 'NodeSchemaCtx<tableCell>',
+  group: 'Table',
+})
+
+/// Schema for table header node.
+export const tableHeaderSchema = $nodeSchema('table_header', () => ({
+  ...originalSchema.table_header,
+  disableDropCursor: true,
+  parseMarkdown: {
+    match: (node) => node.type === 'tableCell' && !!node.isHeader,
+    runner: (state, node, type) => {
+      const align = node.align as string
+      state.openNode(type, { alignment: align })
+      state.openNode(state.schema.nodes.paragraph as NodeType)
+      state.next(node.children)
+      state.closeNode()
+      state.closeNode()
+    },
+  },
+  toMarkdown: {
+    match: (node) => node.type.name === 'table_header',
+    runner: (state, node) => {
+      state.openNode('tableCell')
+      state.next(node.content)
+      state.closeNode()
+    },
+  },
+}))
+
+withMeta(tableHeaderSchema.node, {
+  displayName: 'NodeSchema<tableHeader>',
+  group: 'Table',
+})
+
+withMeta(tableHeaderSchema.ctx, {
+  displayName: 'NodeSchemaCtx<tableHeader>',
+  group: 'Table',
+})
